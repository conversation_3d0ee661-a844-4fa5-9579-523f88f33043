{"name": "mylocobiz", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@fontsource/barlow": "^5.2.5", "@fontsource/roboto": "^5.1.0", "@mui/icons-material": "^6.4.12", "@mui/material": "^6.4.12", "@mui/x-date-pickers": "^7.24.1", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/redux": "^3.6.0", "axios": "^1.7.8", "chart.js": "^4.4.8", "chartjs-plugin-datalabels": "^2.2.0", "dayjs": "^1.11.13", "deep-equal": "^2.2.3", "file-saver": "^2.0.5", "formik": "^2.4.6", "html2canvas": "^1.4.1", "jsdom": "^26.1.0", "moment": "^2.30.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-color": "^2.19.3", "react-dom": "^18.3.1", "react-loader-spinner": "^6.1.6", "react-mui-scheduler": "^2.0.4", "react-pick-color": "^2.0.0", "react-redux": "^8.0.5", "react-router-dom": "^7.0.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "typescript": "^5.0.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "scripts": {"dev": "vite", "start": "env-cmd -f .env.development vite", "build-prod": "env-cmd -f .env.production vite build", "build-staging": "env-cmd -f .env.staging vite build", "build": "vite build", "test": "vitest", "preview": "vite preview"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/deep-equal": "^1.0.4", "@types/file-saver": "^2.0.7", "@types/node": "^22.15.30", "@types/react-redux": "^7.1.25", "@vitejs/plugin-react": "^4.5.1", "ajv": "^7.2.4", "env-cmd": "^10.1.0", "vite": "^5.4.19", "vitest": "^1.6.1"}, "overrides": {"nth-check": "^2.1.1"}}